volumes:
  django-logs:
    driver: local

x-build-args: &build-args
  args:
    BUILD_ENV: ${BUILD_ENV}

services:
  redis:
    image: redis:latest

  django:
    build:
      context: ./django
      <<: *build-args
    volumes:
      - ./django:/app
      - django-logs:/app/logs
    user: root
    depends_on:
      - redis
    env_file: .env
    ports:
      - "5000-5005:5000"
      - "8000-8005:8000"
      - "9000-9005:9000"
