import logging
from rest_framework.views import APIView
from rest_framework import status
from utils import (
    format_response,
    Memoization
)

logger = logging.getLogger('application')


class StatesDropdownAPI(APIView):

    def get(self, request):
        states_list = Memoization.get_states_list()
        data = {'states': states_list}
        return format_response(status.HTTP_200_OK, data, 'Success')
