import logging
from rest_framework.views import APIView
from rest_framework import status
from django.conf import settings
from datetime import datetime
from utils import (
    format_error_response,
    log_user_activity,
    encrypt_password,
    verify_password,
    rehash_password,
    format_response,
    SessionUtils,
    send_email,
    DateUtil,
    get_uuid,
    validate_password,
    Memoization
)
from utils.constants import (
    UserActivityMessages,
    UserActivityType,
    SuccessMessages,
    ErrorMessages,
    EmailHeader,
    OTPConstant,
    TokenType,
    AdminDBColls,
    UserRole,
    UserType,
    CompanyType,
    AppDomainID,
    AppPathID,
)
from utils.mongo import MongoUtility
from .utils import get_redirect_url_data, check_if_active_subscription_exists
from .authentications import (
    AuthenticateAll,
    AuthenticateTokenLogin,
    AuthenticateAccessToken,
    AuthenticateAllorAccessToken,
)

logger = logging.getLogger('application')


class LoginAPI(APIView):

    def post(self, request):
        try:
            email = request.data['email'].strip().lower()
            password = request.data['password']
        except (KeyError, AttributeError, ValueError, TypeError):
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                ErrorMessages.MISSING_EMAIL_PASSWORD,
            )

        if not (email and password):
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                ErrorMessages.USERNAME_OR_PASSWORD_EMPTY,
            )

        session_cls = SessionUtils(email)
        user = session_cls.get_user_record(include_pwd_data=True)
        if not user:
            log_user_activity(
                request, UserActivityType.LOGIN, UserActivityMessages.INCORRECT_USERNAME,
                False, payload={'email': email}
            )
            return format_error_response(
                status.HTTP_403_FORBIDDEN,
                ErrorMessages.COMPANY_USER_NOT_FOUND,
            )

        if not verify_password(password, user['pwd_hash']):
            log_user_activity(request, UserActivityType.LOGIN, UserActivityMessages.INCORRECT_PASSWORD, False, user)
            return format_error_response(
                status.HTTP_401_UNAUTHORIZED,
                ErrorMessages.EMAIL_PASSWORD_COMBINATION_MISMATCH,
            )

        self.db = MongoUtility()

        create_session = True
        if user['user_type'] == UserType.SEEKER.value:
            has_active_sub = check_if_active_subscription_exists(user['company_id'], user['company_type'])
            # only allow seeker admin to login if subscription has ended
            if (not has_active_sub) and (user['user_role'] == UserRole.SEEKER.value):
                create_session = False

        new_pwd_hash = rehash_password(password, user['pwd_hash'])
        if new_pwd_hash:
            pwd_history = user.get('pwd_history', [])
            pwd_history[0] = new_pwd_hash
            self.db.update(AdminDBColls.USERS, {'id': user['id']}, {'pwd_hash': new_pwd_hash, 'pwd_history': pwd_history})

        session = {}
        if create_session:
            session = self.db.find(AdminDBColls.USER_SESSIONS, {'id': user['id']}, find_one=True)
            if session and (not settings.ALLOW_MULTI_SESSION):
                session_cls.delete_session(session['token'])

            if not session:
                session = session_cls.generate_session(user)
        else:
            return format_error_response(
                status.HTTP_403_FORBIDDEN,
                'Subscription has expired. Please contact your Company Admin.',
            )

        if not session:
            return format_error_response(
                status.HTTP_403_FORBIDDEN,
                ErrorMessages.ACCESS_DENIED,
            )

        response_data = {
            'token': session['token'],
            'session_id': session['session_id'],
            'company_id': session['company_id'],
            'company_type': session['company_type'],
            'company_name': session['company_name'],
            'user_id': session['user_id'],
            'user_type': session['user_type'],
            'user_name': session['user_name'],
        }
        redirect_to = get_redirect_url_data(session)

        log_user_activity(request, UserActivityType.LOGIN, UserActivityMessages.LOGIN_SUCCESSFUL, True, user)

        return format_response(status.HTTP_200_OK, response_data, 'Success', redirect_to, set_cookies=True)


class RedirectedLoginAPI(APIView):
    authentication_classes = (AuthenticateTokenLogin, )

    def post(self, request):
        email = request.email

        session_cls = SessionUtils(email)
        user = session_cls.get_user_record()
        if not user:
            log_user_activity(
                request, UserActivityType.LOGIN, UserActivityMessages.INCORRECT_USERNAME,
                False, payload={'email': email}
            )
            return format_error_response(
                status.HTTP_403_FORBIDDEN,
                ErrorMessages.COMPANY_USER_NOT_FOUND,
            )

        db = MongoUtility()

        query = {'id': user['id'], 'company_type': CompanyType.PROVIDER.value}
        session = db.find(AdminDBColls.USER_SESSIONS, query, find_one=True)

        if session and (not settings.ALLOW_MULTI_SESSION):
            session_cls.delete_session(session['token'])

        if not session:
            session = session_cls.generate_session(user)

        if not session:
            return format_error_response(
                status.HTTP_403_FORBIDDEN,
                ErrorMessages.ACCESS_DENIED,
            )

        response_data = {
            'token': session['token'],
            'session_id': session['session_id'],
            'company_id': session['company_id'],
            'company_type': session['company_type'],
            'company_name': session['company_name'],
            'user_id': session['user_id'],
            'user_type': session['user_type'],
            'user_name': session['user_name'],
        }
        redirect_to = get_redirect_url_data(session)

        log_user_activity(request, UserActivityType.REDIRECTED_LOGIN, UserActivityMessages.LOGIN_SUCCESSFUL, True, user)

        return format_response(status.HTTP_200_OK, response_data, 'Success', redirect_to, set_cookies=True)


class LogoutAPI(APIView):
    authentication_classes = (AuthenticateAll, )

    def post(self, request):
        data = {}
        try:
            session_cls = SessionUtils(request.email)
            session_cls.delete_session(request.token)
        except Exception as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                ErrorMessages.TECHNICAL_ERROR,
                e
            )

        log_user_activity(request, UserActivityType.LOGOUT, UserActivityMessages.LOGOUT_SUCCESSFUL)
        redirect_to = Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_LOGIN)
        return format_response(status.HTTP_200_OK, data, 'Success', redirect_to, delete_cookies=True)


class ForgotPassword(APIView):

    def post(self, request):
        try:
            try:
                email = request.data['email'].strip().lower()
            except (KeyError, AttributeError, ValueError, TypeError):
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    'Invalid Email',
                )

            session_cls = SessionUtils(email)
            user = session_cls.get_user_record()
            if not user:
                log_user_activity(
                    request, UserActivityType.FORGOT_PASSWORD, UserActivityMessages.INCORRECT_USERNAME,
                    False, payload={'email': email}
                )
                # sending consistent success response for security reasons
                return format_response(status.HTTP_200_OK, {}, SuccessMessages.EMAIL_SENT)

            db = MongoUtility()

            token_query = {'email': email, 'token_type': TokenType.PASSWORD_RESET.name}
            access_token = db.find(AdminDBColls.ACCESS_TOKEN, token_query, find_one=True)

            now = DateUtil.get_current_timestamp()
            if access_token and access_token.get('expiry_time') and access_token.get('expiry_time') > now:
                # sending consistent success response for security reasons
                return format_response(status.HTTP_200_OK, {}, SuccessMessages.EMAIL_SENT)

            # GENERATE A UUID AS MAIL TOKEN & ASSIGN EXPIRY TIME FOR THAT TOKEN,
            # USE THE RESPOSNE OF THE GET API TO CHECK IF THE TOKEN IS VALID OR NOT
            access_token = get_uuid()
            reset_password_url = f"{settings.AUTH_APP_URL.strip('/')}/reset-password?access_token={access_token}"
            message = {
                'reset_password_url': reset_password_url,
                'year': datetime.now().year
            }
            send_email([email], [], EmailHeader.SCLEN_RESET_PASSWORD, message, template='reset_password.html')

            access_token_data = {
                "email": email,
                "user_id": user['id'],
                "token": access_token,
                "token_type": TokenType.PASSWORD_RESET.name,
                "company_type": user['company_type'],
                "created_on": now,
                "expiry_time": now + OTPConstant.EXPIRE_TIME,
                "datetime": DateUtil.get_current_timestamp(True)
            }
            db.update(AdminDBColls.ACCESS_TOKEN, token_query, access_token_data, upsert=True)

            log_user_activity(request, UserActivityType.FORGOT_PASSWORD, UserActivityMessages.FORGOT_PASS_SUCCESS, True, user)
        except Exception as e:
            logger.error('[LOGIN_ERROR] {}'.format(e))
            return format_error_response(status.HTTP_500_INTERNAL_SERVER_ERROR, ErrorMessages.TECHNICAL_ERROR)
        return format_response(status.HTTP_200_OK, {}, SuccessMessages.EMAIL_SENT)


class ResetPassword(APIView):
    authentication_classes = (AuthenticateAccessToken, )

    def post(self, request):
        try:
            try:
                new_password = request.data['new_password']
                confirm_password = request.data['confirm_password']
            except (KeyError, AttributeError, ValueError, TypeError):
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.MISSING_EMAIL_PASSWORD,
                )

            email = request.email

            if new_password != confirm_password:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.NEW_PASSWORD_AND_CONFIRM_PASSWORD_MISMACTH,
                )

            session_cls = SessionUtils(email)
            user = session_cls.get_user_record()
            if not user:
                log_user_activity(
                    request, UserActivityType.RESET_PASSWORD, UserActivityMessages.INCORRECT_USERNAME,
                    False, payload={'email': email}
                )
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.COMPANY_USER_NOT_FOUND,
                )

            new_pwd_hash = encrypt_password(new_password)
            errors = validate_password(new_password, new_pwd_hash, user)
            if errors:
                return format_error_response(status.HTTP_400_BAD_REQUEST, errors[0], errors)

            session_cls.delete_session()
            user = session_cls.update_password(new_pwd_hash)

            log_user_activity(request, UserActivityType.RESET_PASSWORD, UserActivityMessages.RESET_PASS_SUCCESS, user_data=user)
        except Exception:
            return format_error_response(status.HTTP_400_BAD_REQUEST, ErrorMessages.TECHNICAL_ERROR)
        return format_response(status.HTTP_200_OK, {}, 'Success')


class ChangePassword(APIView):
    authentication_classes = (AuthenticateAllorAccessToken, )

    def post(self, request):
        try:
            try:
                current_password = request.data['current_password']
                new_password = request.data['new_password']
                confirm_password = request.data['confirm_password']
            except KeyError as e:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f'Missing {e}',
                )

            if current_password == new_password:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.CURRENT_PASSWORD_AND_NEW_PASSWORD_SAME,
                )

            if new_password != confirm_password:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.NEW_PASSWORD_AND_CONFIRM_PASSWORD_MISMACTH,
                )

            session_cls = SessionUtils(request.email)
            user = session_cls.get_user_record(include_pwd_data=True)
            if not user:
                log_user_activity(request, UserActivityType.CHANGE_PASSWORD, UserActivityMessages.INCORRECT_USERNAME, False)
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.COMPANY_USER_NOT_FOUND,
                )

            if not verify_password(current_password, user['pwd_hash']):
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    ErrorMessages.OLD_PASSWORD_DOESNT_MISMACTH,
                )

            new_pwd_hash = encrypt_password(new_password)
            errors = validate_password(new_password, new_pwd_hash, user)
            if errors:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    errors[0],
                    errors
                )

            session_cls.delete_session(request.token)
            user = session_cls.update_password(new_pwd_hash)

            log_user_activity(request, UserActivityType.CHANGE_PASSWORD, UserActivityMessages.CHANGE_PASS_SUCCESS, user_data=user)
        except Exception as e:
            logger.error(f'[CHANGE_PASS_ERROR] {e}')
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                ErrorMessages.TECHNICAL_ERROR,
            )
        return format_response(status.HTTP_200_OK, {}, 'Success')


class GetSessionDataAPI(APIView):
    authentication_classes = (AuthenticateAll, )

    def get(self, request):
        session = {
            'token': request.token,
            'session_id': request.session_id,
            'company_id': request.company_id,
            'company_type': request.company_type,
            'company_name': request.company_name,
            'user_id': request.user_id,
            'user_type': request.user_type,
            'user_role': request.user_role,
            'user_name': request.user_name,
        }

        redirect_to = get_redirect_url_data(session)

        response_data = session
        return format_response(status.HTTP_200_OK, response_data, 'Success', redirect_to)
