from utils import Memoization
from utils.constants import (
    UserRole,
    AppDomainID,
    AppPathID,
    DBColls,
    CompanyType,
    SubscriptionStatus
)
from utils.mongo import MongoUtility


def check_if_active_subscription_exists(company_id, company_type):
    if company_type == CompanyType.PROVIDER.value:
        return True

    db = MongoUtility()
    query = {
        'company_id': company_id,
        'status_id': {'$in': [SubscriptionStatus.TRIAL.value, SubscriptionStatus.ACTIVE.value]}
    }
    active_sub = db.find(DBColls.SUBSCRIPTIONS, query, find_one=True)
    return bool(active_sub)


def get_redirect_url_data(session, has_active_sub):
    user_role = session['user_role']

    if user_role == UserRole.SUPER_ADMIN.value:
        domain_id, path_id = AppDomainID.SUPERADMIN, AppPathID.SADMIN_CONFIG
    elif user_role == UserRole.ADMIN_SEEKER.value:
        domain_id, path_id = AppDomainID.ADMIN, AppPathID.ADMIN_PROFILE
    else:
        domain_id, path_id = AppDomainID.PROCUREMENT, AppPathID.PROCUREMENT_RFQ_DASHBOARD

    return Memoization.get_app_url_data(domain_id, path_id, company_type=session['company_type'])
