from rest_framework import authentication, exceptions, status
from utils import ErrorResponse, RedisUtils, DateUtil
from utils.constants import (
    ErrorMessages, DBColls,
    CompanyType,
    UserRole,
    UserType
)
from utils.mongo import MongoUtility


def get_session_data(request, raise_error=False):
    request.is_token_valid = False
    request.is_seeker = False
    request.is_seeker_admin = False
    request.is_provider = False
    request.is_super_admin = False

    headers = request.headers
    user_agent = headers.get('user-agent', 'Unknown')
    token = headers.get('token', None)

    redis_inst = RedisUtils()
    session = {}
    if token:
        session = redis_inst.get_data(token)
        if not session:
            db = MongoUtility()
            session = db.find(DBColls.USER_SESSIONS, {'token': token}, {'_id': 0, 'datetime': 0}, find_one=True)
            redis_inst.set_data(token, session)

    if not session:
        if raise_error:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.AUTHENTICATION_ERROR,
                    ErrorMessages.INVALID_TOKEN,
                    status.HTTP_401_UNAUTHORIZED
                )
            )
        return request

    request.user_agent = user_agent
    request.token = token
    request.now = DateUtil.get_current_timestamp()
    request.session_id = session.get('session_id')
    request.force_auth = session.get('force_auth', False)
    request.is_sso_login = session.get('is_sso_login', False)
    request.company_id = session.get('company_id')
    request.company_type = session.get('company_type')
    request.company_name = session.get('company_name')
    request.user_id = session.get('user_id')
    request.user_type = session.get('user_type')
    request.user_role = session.get('user_role')
    request.user_name = session.get('user_name', '')
    request.email = session.get('email')
    request.user_permissions = session.get('permissions', [])
    request.is_seeker = (request.user_type == UserType.SEEKER.value) and (request.company_type == CompanyType.SEEKER.value)
    request.is_seeker_admin = request.is_seeker and (request.user_role == UserRole.ADMIN_SEEKER.value)
    request.is_provider = (request.user_type == UserType.PROVIDER.value) and (request.company_type == CompanyType.PROVIDER.value)
    request.is_super_admin = (request.user_type == UserType.SCLEN.value) and (request.user_role == UserRole.SUPER_ADMIN.value)
    request.is_token_valid = True
    return request


class AuthenticateSuperAdmin(authentication.BaseAuthentication):
    """This authentication is used to only authorize seeker admins."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not request.is_super_admin:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateSeekerAdmin(authentication.BaseAuthentication):
    """This authentication is used to only authorize seeker admins."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_seeker_admin):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateSeeker(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_seeker):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateProvider(authentication.BaseAuthentication):
    """This authentication is used to only authorize providers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_provider):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateAll(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers or providers. This does not authorize drivers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_seeker or request.is_provider):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateAccessToken(authentication.BaseAuthentication):

    def authenticate(self, request):
        request.is_token_valid = False

        headers = request.headers
        user_agent = headers.get('user-agent', 'Unknown')
        token = headers.get('token', None)

        if token:
            admin_db = MongoUtility()
            session = admin_db.find(DBColls.ACCESS_TOKEN, {'token': token}, find_one=True)
            if session:
                request.user_agent = user_agent
                request.token = token
                request.now = DateUtil.get_current_timestamp()
                request.force_auth = session.get('force_auth', False)
                request.is_sso_login = session.get('is_sso_login', False)
                request.company_id = session.get('company_id')
                request.company_type = session.get('company_type')
                request.user_id = session.get('user_id')
                request.email = session.get('email')
                request.user_type = session.get('user_type')
                request.user_name = session.get('user_name', '')
                request.is_token_valid = session['expiry_time'] > request.now

        if not request.is_token_valid:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.AUTHENTICATION_ERROR,
                    ErrorMessages.INVALID_TOKEN,
                    status.HTTP_401_UNAUTHORIZED
                )
            )
        return ({}, None)


class AuthenticateAllorAccessToken(authentication.BaseAuthentication):

    def __init__(self):
        # List your authentication classes in the order of preference
        self.auth_classes = [AuthenticateAll(), AuthenticateAccessToken()]

    def authenticate(self, request):
        for auth_class in self.auth_classes:
            try:
                # Try authenticating with each class
                user_auth_tuple = auth_class.authenticate(request)
                if user_auth_tuple:
                    return user_auth_tuple
            except exceptions.AuthenticationFailed:
                continue  # Ignore the error and try the next class

        # If none of the classes succeeded, raise an error
        raise exceptions.AuthenticationFailed(
            ErrorResponse.get_response_obj(
                ErrorResponse.AUTHENTICATION_ERROR,
                ErrorMessages.INVALID_TOKEN,
                status.HTTP_401_UNAUTHORIZED
            )
        )
