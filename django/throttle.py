import re
from rest_framework.throttling import ScopedRateThrottle


class ResendVerificationEmailThrottle(ScopedRateThrottle):

    def parse_rate(self, rate: str) -> tuple[int, int]:
        """
        Given the request rate string, return a two tuple of.

        <allowed number of requests>, <period of time in seconds>
        """
        if rate is None:
            return (None, None)

        try:
            num_requests, duration, period = re.match(r'(\d+)/(\d+)(\D+)', rate).groups()
        except AttributeError:
            raise ValueError('Invalid rate limiter string format.')

        num_requests = int(num_requests)
        duration = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}[period[0]] * int(duration)
        return (num_requests, duration)
