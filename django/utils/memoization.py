from utils.mongo import MongoUtility
from utils.constants import AdminDBColls


class Memoization:
    sms_vendor_configs = {}
    states_list = []
    app_domains = {}
    app_paths = {}

    @staticmethod
    def get_sms_vendor_config(vendor_type):
        if vendor_type in Memoization.sms_vendor_configs:
            return Memoization.sms_vendor_configs[vendor_type]

        db = MongoUtility()
        config = db.find(AdminDBColls.SMS_VENDORS, {'id': vendor_type}, find_one=True)
        Memoization.sms_vendor_configs[vendor_type] = config
        return config

    @staticmethod
    def get_states_list():
        if Memoization.states_list:
            return Memoization.states_list

        db = MongoUtility()
        states_list = [x for x in db.find(AdminDBColls.STATES_LIST, {}, sort=[('name', 1)])]
        Memoization.states_list = states_list
        return states_list

    @staticmethod
    def get_app_url_data(domain_id, path_id, **kwargs):
        if (domain_id not in Memoization.app_domains) or (path_id not in Memoization.app_paths):
            db = MongoUtility()
            obj = db.find(AdminDBColls.APP_URLS, {'id': domain_id}, find_one=True)

            if domain_id not in Memoization.app_domains:
                Memoization.app_domains[domain_id] = obj.get('fe_domain') or ''

            fe_paths = obj.get('fe_paths') or {}
            for key, value in fe_paths.items():
                if key not in Memoization.app_paths:
                    Memoization.app_paths[key] = value

        domain = Memoization.app_domains.get(domain_id) or ''
        path = Memoization.app_paths.get(path_id, '').format(**kwargs)

        return {
            'domain': domain,
            'path': path,
            'url': f'{domain}{path}',
        }
