import jwt
import json
import pickle
import redis
import logging
from django.conf import settings
from utils.mongo import MongoUtility
from utils.constants import (
    LoginType,
    AdminDBColls,
    AccountStatus
)
from .common import get_client_details, get_uuid, get_domain_from_url
from .date_util import DateUtil

logger = logging.getLogger('application')


class JWTToken:
    HS256 = 'HS256'

    class Expiry:
        EMAIL_VERIFICATION = 1440  # 24hours
        RESET_PASSWORD = 15  # minutes

    class Purpose:
        VERIFY_COMPANY = 'verify_company'
        VERIFY_USER = 'verify_user'
        VERIFY_COMPANY_AND_USER = 'verify_company_and_user'
        ACTIVATE_USER = 'activate_user'

    @staticmethod
    def get_token(payload: dict, subject: str, purpose: str, expiry: int) -> str:
        issuer = get_domain_from_url(settings.INTERNAL_SERVER_URL)
        issued_at = int(DateUtil.get_current_timestamp() / 1000)
        expires_on = issued_at + (expiry * 60)
        payload.update({
            "iss": issuer,
            "sub": subject,
            "iat": issued_at,
            "exp": expires_on,
            "purpose": purpose,  # custom claim for clarity
        })
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm=JWTToken.HS256)
        return token

    @staticmethod
    def decode_token(token: str, issuer: str = settings.INTERNAL_SERVER_URL, secret: str = settings.SECRET_KEY) -> dict:
        issuer = get_domain_from_url(settings.INTERNAL_SERVER_URL)
        options = {"require": ["iss", "sub", "iat", "exp"]}  # required claims

        try:
            decoded_payload = jwt.decode(token, secret, issuer=issuer, options=options, algorithms=[JWTToken.HS256])
        except (jwt.ExpiredSignatureError, jwt.InvalidIssuerError, jwt.InvalidAudienceError, jwt.MissingRequiredClaimError) as e:
            raise ValueError(f'Token error: {e}')
        except jwt.exceptions.DecodeError:
            raise ValueError(f'Invalid token')
        return decoded_payload


class RedisUtils(object):

    def __init__(self, db_url: str = settings.REDIS_SESSION_DB_URL):
        self.redis_conn = redis.from_url(db_url)

    def list_keys(self):
        """List all keys from current DB."""
        return self.redis_conn.keys('*')

    def get_data(self, key: str) -> dict:
        """Get data stored using JSON format."""
        data = self.redis_conn.get(key)
        return json.loads(data) if data else {}

    def get_pdata(self, key: str) -> dict:
        """Get data stored using Pickle format."""
        data = self.redis_conn.get(key)
        return pickle.loads(data) if data else {}

    def set_data(self, key: str, data: dict) -> bool:
        """Set data using JSON format."""
        return self.redis_conn.set(key, json.dumps(data))

    def set_pdata(self, key: str, data: dict) -> bool:
        """Set data using Pickle format."""
        return self.redis_conn.set(key, pickle.dumps(data))

    def delete_data(self, key: str = None, flush_db: bool = False, flush_all: bool = False) -> int:
        """Delete specific key data or flush_db (current DB) or flush_all (data in all DBs)."""
        res = 0
        if key:
            res = self.redis_conn.delete(key)
        elif flush_db:
            res = self.redis_conn.flushdb()
        elif flush_all:
            res = self.redis_conn.flushall()
        return res


class SessionUtils(object):

    def __init__(self, email, is_sso_login=False):
        self.email = email.lower().strip()
        self.is_sso_login = is_sso_login
        self.db = MongoUtility()
        self.now = DateUtil.get_current_timestamp()

    def get_company_record(self, company_id, df={'_id': 0}):
        return self.db.find(AdminDBColls.COMPANIES, {'id': company_id}, df, find_one=True)

    def get_user_record(self, include_pwd_data=False):
        query = {
            'email': self.email,
            'is_active': True,
        }
        data_filter = {
            '_id': 0,
            'id': 1,
            'company_id': 1,
            'company_name': 1,
            'company_type': 1,
            'email': 1,
            'user_name': 1,
            'user_type': 1,
            'user_role': 1,
            'email_verified_on': 1,
        }
        if include_pwd_data:
            data_filter.update({
                'pwd_hash': 1,
                'pwd_changed_on': 1,
                'pwd_history': 1,
            })

        self.user = self.db.find(AdminDBColls.USERS, query, data_filter, find_one=True)

        if self.user:
            df = {'_id': 0, 'acc_status_id': 1, 'email_verified_on': 1}
            company_data = self.get_company_record(self.user['company_id'], df)

            self.user.update({
                'is_acc_active': company_data.get('acc_status_id') == AccountStatus.ACTIVE.value,
                'is_email_verified': bool(company_data.get('email_verified_on') and self.user['email_verified_on'])
            })
        return self.user

    def generate_session(self, user_record={}, store_session=True):
        user_session = user_record
        if not user_session:
            logger.warning('User not found')
            return {}

        user_perm_docs = self.db.find(AdminDBColls.USER_PERMISSIONS, {'user_role': user_record.get('user_role')})
        user_permissions = []
        for doc in user_perm_docs:
            user_permissions.extend(doc['permissions'])

        user_session.update({
            'user_id': user_session['id'],
            'token': get_uuid(),
            'session_id': get_uuid(),
            'is_sso_login': self.is_sso_login,
            'created_on': self.now,
            'datetime': DateUtil.get_current_timestamp(True),
            'permissions': user_permissions
        })

        if store_session:
            query = {
                'email': self.email,
            }
            self.db.update(AdminDBColls.USER_SESSIONS, query, user_session, upsert=True)
            user_session.pop('_id', None)
            user_session.pop('datetime', None)

            redis_inst = RedisUtils()
            redis_inst.set_data(user_session['token'], user_session)
        return user_session

    @staticmethod
    def set_session_cookie(response, token: str):
        response.set_cookie(
            key="token",
            value=token,
            httponly=settings.SESSION_COOKIE_HTTPONLY,
            secure=settings.SESSION_COOKIE_SECURE,
            samesite=settings.SESSION_COOKIE_SAMESITE,
            domain=settings.API_SERVER_DOMAIN
            # No 'max_age' or 'expires' = session cookie
        )

    @staticmethod
    def delete_session_cookie(response):
        response.delete_cookie('token')

    def delete_session(self, token=None):
        if not token:
            user_session = self.db.find(AdminDBColls.USER_SESSIONS, {'email': self.email}, find_one=True)
            if not user_session:
                return None
            token = user_session['token']
        self.db.delete(AdminDBColls.USER_SESSIONS, {'token': token}, delete_many=True)

        redis_inst = RedisUtils()
        redis_inst.delete_data(token)

    def update_password(self, new_pwd_hash):
        query = {
            'email': self.email,
        }

        pwd_history = self.user.get('pwd_history', [])
        pwd_history.insert(0, new_pwd_hash)

        update_query = {
            'pwd_hash': new_pwd_hash,
            'pwd_history': pwd_history[:5],
            'pwd_changed_on': self.now,
        }
        user = self.db.update(AdminDBColls.USERS, query, update_query, find_one_and_update=True)
        user.pop('_id', None)
        return user

    def get_blocked_user(self, login_type, email='', contact_number=''):
        filter_key = 'email' if email else 'contact_number'
        filter_value = email if email else contact_number

        blocked_user = self.db.find(AdminDBColls.BLOCKED_USERS, {filter_key: filter_value, 'login_type': login_type.value}, find_one=True)
        return blocked_user

    def login_attempts_data_update(self, max_login_attempts, email_login_details: dict = None, otp_login_details: dict = None):
        filter_query = {}
        if email_login_details:
            filter_query = {
                'email': email_login_details['email'],
                'login_type': LoginType.PASSWORD_LOGIN.value
            }
        elif otp_login_details:
            filter_query = {
                'login_type': LoginType.OTP_LOGIN.value
            }
            if 'contact_number' in otp_login_details:
                filter_query['contact_number'] = otp_login_details['contact_number']
            else:
                filter_query['email'] = otp_login_details.get('email')

        blocked_user = self.db.find(AdminDBColls.BLOCKED_USERS, filter_query, find_one=True)
        login_attempts = blocked_user.get('login_attempts', 0) + 1

        update_query = {
            "login_attempts": login_attempts,
            "block_user": login_attempts > max_login_attempts,
            "created": self.now,
            'datetime': self.datetime_obj,
        }

        blocked_user = self.db.update(AdminDBColls.BLOCKED_USERS, filter_query, update_query, upsert=True, find_one_and_update=True)
        blocked_user.pop('_id', None)
        return blocked_user

    def reset_login_attempt_details(self, login_type, email='', contact_number=''):
        # on successful login both otp and password blocks should get reset for the user
        query = {'email': email} if email else {'contact_number': contact_number}
        self.db.delete(AdminDBColls.BLOCKED_USERS, query, delete_many=True)


def log_user_activity(request, activity_type, message, is_success=True, user_data={}, payload={}):
    db = MongoUtility()
    if user_data:
        basic_user_data = {
            'user_id': user_data.get('id'),
            'user_name': user_data.get('user_name'),
            'company_type': user_data.get('company_type'),
            'company_id': user_data.get('company_id'),
        }
    elif is_success:
        basic_user_data = {
            'user_id': request.user_id,
            'user_name': request.user_name,
            'company_type': request.company_type,
            'company_id': request.company_id,
        }
    else:
        basic_user_data = {}

    obj = {
        **basic_user_data,
        'datetime': DateUtil.get_current_timestamp(True),
        'created_on': DateUtil.get_current_timestamp(),
        'message': message,
        'data': payload,
        'activity_type': activity_type,
        'is_success': is_success,
    }
    obj.update(get_client_details(request))
    db.insert(AdminDBColls.USER_ACTIVITY_LOGS, [obj])
