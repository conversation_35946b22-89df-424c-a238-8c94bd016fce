import re
import string
import math
import hashlib
import requests
import logging
from difflib import SequenceMatcher
from django.core.exceptions import (
    ValidationError,
    ImproperlyConfigured
)
from utils import DateUtil

logger = logging.getLogger('application')


class ValidatorClass:
    HISTORIC_PASSWORD_VALIDATOR = 'HistoricPassword'
    MINIMUM_LENGTH_VALIDATOR = 'MinimumLengthValidator'
    NUMERIC_PASSWORD_VALIDATOR = 'NumericPasswordValidator'
    MINIMUM_UPPERCASE_VALIDATOR = 'MinimumUpperCaseValidator'
    MINIMUM_LOWERCASE_VALIDATOR = 'MinimumLowerCaseValidator'
    MINIMUM_DIGITS_VALIDATOR = 'MinimumDigitsValidator'
    MINIMUM_SPECIAL_CHARS_VALIDATOR = 'MinimumSpecialCharsValidator'
    ALPHANUMERIC_VALIDATOR = 'AlphaNumericPasswordValidator'
    MAXIMUM_LENGTH_VALIDATOR = 'MaximumLengthValidator'
    USER_ATTRIBUTE_SIMILARITY_VALIDATOR = 'UserAttributeSimilarityValidator'
    PASSWORD_SEQUENCE_CHECK = 'PasswordSequenceCheck'
    PASSWORD_VALIDITY_VALIDATOR = 'PasswordValidity'
    COMMON_PASSWORD_VALIDATOR = 'CommonPasswordValidator'


DEFAULT_PASSWORD_VALIDATORS = [
    {'is_active': True, 'name': ValidatorClass.HISTORIC_PASSWORD_VALIDATOR, 'options': {'history_length': 5}},
    {'is_active': True, 'name': ValidatorClass.MINIMUM_LENGTH_VALIDATOR, 'options': {'min_length': 8}},
    {'is_active': True, 'name': ValidatorClass.NUMERIC_PASSWORD_VALIDATOR},
    {'is_active': True, 'name': ValidatorClass.MINIMUM_UPPERCASE_VALIDATOR, 'options': {'min_length': 1}},
    {'is_active': True, 'name': ValidatorClass.MINIMUM_LOWERCASE_VALIDATOR, 'options': {'min_length': 1}},
    {'is_active': True, 'name': ValidatorClass.MINIMUM_DIGITS_VALIDATOR, 'options': {'min_length': 1}},
    {'is_active': True, 'name': ValidatorClass.MINIMUM_SPECIAL_CHARS_VALIDATOR, 'options': {'min_length': 1}},
    {'is_active': False, 'name': ValidatorClass.ALPHANUMERIC_VALIDATOR},
    {'is_active': True, 'name': ValidatorClass.MAXIMUM_LENGTH_VALIDATOR, 'options': {'max_length': 64}},
    {'is_active': True, 'name': ValidatorClass.USER_ATTRIBUTE_SIMILARITY_VALIDATOR, 'options': {'max_similarity': 0.7, 'user_attributes': ['user_name', 'email']}},
    {'is_active': True, 'name': ValidatorClass.PASSWORD_SEQUENCE_CHECK, 'options': {'sequence_length': 4}},
    {'is_active': True, 'name': ValidatorClass.PASSWORD_VALIDITY_VALIDATOR, 'options': {'no_of_days': 90}},
    {'is_active': True, 'name': ValidatorClass.COMMON_PASSWORD_VALIDATOR},
]

KEYBOARD_SEQUENCES = [
    'qwertyuiop',
    'poiuytrewq',
    'asdfghjkl',
    'lkjhgfdsa',
    'zxcvbnm',
    'mnbvcxz',
    '0987654321',
    '1234567890',
]


def validate_password(password, password_hash, user_details=None, password_validators=DEFAULT_PASSWORD_VALIDATORS, skip_validators=[ValidatorClass.PASSWORD_VALIDITY_VALIDATOR]):
    errors = []
    password_validators = get_password_validators(password_validators, skip_validators)

    common_pass_validator = None
    for validator in password_validators:
        try:
            if validator.name == ValidatorClass.HISTORIC_PASSWORD_VALIDATOR:
                validator.validate(password_hash, user_details)
            elif validator.name == ValidatorClass.COMMON_PASSWORD_VALIDATOR:
                # to be executed at the end and only if there are no other errors
                common_pass_validator = validator
                continue
            else:
                validator.validate(password, user_details)
        except ValidationError as error:
            errors.append(error.message)

    # since common_pass_validator has a third-party api dependency
    # making sure common_pass_validator is executed only if all other validators have passed
    if not errors and common_pass_validator:
        try:
            common_pass_validator.validate(password, user_details)
        except ValidationError as error:
            errors.append(error.message)
    return errors


def get_password_validators(password_validators, skip_validators):
    validators = []
    for validator in password_validators:
        if (not validator.get('is_active')) or (validator['name'] in skip_validators):
            continue

        try:
            validator_class = globals()[validator['name']]
            validator_class.name = validator['name']
        except ImportError:
            msg = f"The module {validator['name']} in NAME could not be imported, Check your PASSWORD_VALIDATORS."
            raise ImproperlyConfigured(msg)

        validators.append(validator_class(**validator.get('options', {})))
    return validators


class HistoricPassword:
    """
        Validate whether the password was used before.
    """

    def __init__(self, history_length):
        self.history_length = history_length

    def validate(self, password, user_details=None):
        password_history = user_details.get('pwd_history', [])
        if password in password_history[:self.history_length]:
            raise ValidationError(f"New Password cannot be same as last {self.history_length} passwords.")


class MinimumLengthValidator:
    """
        Validate whether the password is of a minimum length.
    """

    def __init__(self, min_length=8):
        self.min_length = min_length

    def validate(self, password, user_details=None):
        if len(password) < self.min_length:
            raise ValidationError(f"Password must be at least {self.min_length} characters long.")


class NumericPasswordValidator:
    """
        Validate whether the password is Numeric.
    """

    def validate(self, password, user_details=None):
        if password.isdigit():
            raise ValidationError("Password cannot be entirely numeric.")


class MinimumUpperCaseValidator:
    """
        Validate whether the password has min length uppercase letter.
    """

    def __init__(self, min_length=1):
        self.min_length = min_length

    def validate(self, password, user_details=None):
        uppercase_letters = [char for char in password if char.isupper()]
        if len(uppercase_letters) < self.min_length:
            raise ValidationError(f"Password must contain at least {self.min_length} Uppercase letter(s).")


class MinimumLowerCaseValidator:
    """
        Validate whether the password has min length lowercase letter.
    """

    def __init__(self, min_length=1):
        self.min_length = min_length

    def validate(self, password, user_details=None):
        lowercase_letters = [char for char in password if char.islower()]
        if len(lowercase_letters) < self.min_length:
            raise ValidationError(f"Password must contain at least {self.min_length} Lowercase letter(s).")


class MinimumDigitsValidator:
    """
        Validate whether the password has min length of digits.
    """

    def __init__(self, min_length=1):
        self.min_length = min_length

    def validate(self, password, user_details=None):
        digits = [c for c in password if c.isdigit()]
        if len(digits) < self.min_length:
            raise ValidationError(f"Password must contain at least {self.min_length} Digit(s).")


class MinimumSpecialCharsValidator:
    """
        Validate whether the password has min length of special chars.
    """

    def __init__(self, min_length=1):
        self.min_length = min_length

    def validate(self, password, user_details=None):
        special_chars = [c for c in password if c in string.punctuation]
        if len(special_chars) < self.min_length:
            raise ValidationError(f"Password must contain at least {self.min_length} Special character(s).")


class AlphaNumericPasswordValidator:
    """
        Validate whether the password is alphanumeric (includes special charcters).
    """

    def validate(self, password, user_details=None):
        has_char = any(c.isalpha() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in string.punctuation for c in password)

        if not has_char or not has_digit or not has_special:
            raise ValidationError("Password should be Apha numeric only.")


class MaximumLengthValidator:
    """
        Validate whether the password is of a maximum length.
    """

    def __init__(self, max_length=16):
        self.max_length = max_length

    def validate(self, password, user_details=None):
        if len(password) > self.max_length:
            raise ValidationError(f"Password too long. Max {self.max_length} characters allowed.")


class UserAttributeSimilarityValidator:

    def __init__(self, user_attributes, max_similarity):
        self.user_attributes = user_attributes
        self.max_similarity = max_similarity

    def validate(self, password, user_details=None):
        if not user_details:
            return

        for attribute_name in self.user_attributes:
            value = user_details.get(attribute_name)

            if not value or not isinstance(value, str):
                continue

            """
                splitting the original string at non-word characters, and the original string is also included in the list.
                string = "Hello, World! This is a test."
                After reg ex : ['Hello', 'World', 'This', 'is', 'a', 'test', 'Hello, World! This is a test.']
            """
            value_parts = re.split(r'\W+', value) + [value]
            for value_part in value_parts:
                if SequenceMatcher(a=password.lower(), b=value_part.lower()).quick_ratio() >= self.max_similarity:
                    raise ValidationError(f"Password is too similar to the {attribute_name}.")


class PasswordSequenceCheck:
    """
        Validate whether the password follows any alpha or num sequence.
    """

    def __init__(self, sequence_length=2):
        self.sequence_length = sequence_length

    def generate_substrings(self, input_list):
        substrings = []
        for input_string in input_list:
            length = len(input_string)
            for start in range(length):
                for end in range(start + 1, length + 1):
                    substring = input_string[start:end]
                    substrings.append(substring)
        return substrings

    def validate(self, password, user_details=None):
        key_seq_substrings = self.generate_substrings(KEYBOARD_SEQUENCES)
        for i in range(len(password) - self.sequence_length + 1):
            sequence = password[i: i + self.sequence_length]
            if all(ord(sequence[j]) == ord(sequence[j - 1]) + 1 for j in range(1, self.sequence_length)):
                raise ValidationError(f"Password cannot have more than {self.sequence_length - 1} running sequence characters.")
            elif all(ord(sequence[j]) == ord(sequence[j - 1]) - 1 for j in range(1, self.sequence_length)):
                raise ValidationError(f"Password cannot have more than {self.sequence_length - 1} running sequence characters.")
            elif sequence in key_seq_substrings:
                raise ValidationError(f"Password cannot have more than {self.sequence_length - 1} running keyboard sequence characters.")


class PasswordValidity:
    """
        Validate whether the password was used before.
    """

    def __init__(self, no_of_days):
        self.no_of_days = no_of_days

    def validate(self, password, user_details):
        now = DateUtil.get_current_timestamp()
        pwd_changed_on = user_details.get('pwd_changed_on')
        if not pwd_changed_on:
            return

        diff_time = (now - pwd_changed_on)
        diff_in_days = math.floor(diff_time / 86400000)
        if diff_in_days > int(self.no_of_days):
            raise ValidationError("Password Expired. Please Reset the Password.")


class CommonPasswordValidator:
    """
        Validate whether the password is a common password, PARKING IT FOR NOW.

        The password is rejected if it occurs in a provided list, which may be gzipped.
        The list Django ships with contains 1000 common passwords, created by Mark Burnett:
        https://xato.net/passwords/more-top-worst-passwords/
    """

    @staticmethod
    def generate_sha1_hash(password: str) -> str:
        # Create a new sha1 hash object
        sha1 = hashlib.sha1()

        # Update the hash object with the password (encode it to bytes)
        sha1.update(password.encode('utf-8'))

        # Return the hexadecimal representation of the hash
        return sha1.hexdigest()

    def validate(self, password, user_details=None):
        # Visit this link for more info on how it works
        # https://haveibeenpwned.com/API/v3#PwnedPasswords
        sha1_pwd_hash = self.generate_sha1_hash(password).upper()
        prefix, suffix = sha1_pwd_hash[:5], sha1_pwd_hash[5:]

        url = f'https://api.pwnedpasswords.com/range/{prefix}'

        try:
            response = requests.request("GET", url)
        except Exception as e:
            logger.error(f'Failed to check for common passwords | error: {str(e)}')
            return

        if response.status_code == 200:
            for item in response.text.split('\r\n'):
                pwd_suffix_hash, count = item.split(':')
                if pwd_suffix_hash == suffix:
                    raise ValidationError(f"Password is too common. Please use a unique and strong password.")
        else:
            logger.error(f'Failed to check for common passwords | status_code: {response.status_code} | msg: {response.text}')
