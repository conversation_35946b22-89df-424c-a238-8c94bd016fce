from zoneinfo import ZoneInfo
from enum import Enum

IST_TZ = ZoneInfo('Asia/Kolkata')
UTC_TZ = ZoneInfo('UTC')


class HeaderKeys:
    HTTP_REQUEST_ID = 'HTTP_REQUEST_ID'
    HTTP_TOKEN = 'HTTP_TOKEN'
    HTTP_USER_AGENT = 'HTTP_USER_AGENT'
    HTTP_FORWARDED_FOR = 'HTTP_X_FORWARDED_FOR'
    REMOTE_ADDR = 'REMOTE_ADDR'


class AppDomainID:
    AUTH = 'auth'
    ADMIN = 'admin'
    SUPERADMIN = 'superadmin'
    PROCUREMENT = 'procurement'
    NETWORK = 'network'


class AppPathID:
    # auth paths
    AUTH_LOGIN = 'auth_login'
    AUTH_VERIFY_EMAIL = 'auth_verify_email'
    AUTH_VERIFY_EMAIL_SUCCESS = 'auth_verify_email_success'
    AUTH_VERIFY_EMAIL_FAILED = 'auth_verify_email_failed'
    # superadmin paths
    SADMIN_CONFIG = 'sadmin_config'
    # admin paths
    ADMIN_CONFIRM_PLAN = 'admin_confirm_plan'
    ADMIN_PROFILE = 'admin_profile'
    # procurement paths
    PROCUREMENT_RFQ_DASHBOARD = 'procurement_rfq_dashboard'


class SuccessMessages:
    SUCCESS = 'Success'
    OKAY = 'Looks like everything went okay'
    UPDATE_SUCCESS = 'Data updated successfully'
    RETRIEVE_SUCCESS = 'Data retrieved successfully'
    OTP_SENT = 'OTP sent successfully'
    EMAIL_SENT = 'You will receive a reset link if your email is registered'
    PASSWORD_UPDATE_SUCCESS = 'Password reset success'


class ErrorMessages:
    INVALID_SESSION_ID = 'Invalid session id'
    INVALID_TOKEN = 'Invalid token'
    MISSING_TOKEN = 'Token missing'
    ACCESS_DENIED = 'Access Denied'
    PASSWORD_ERROR = 'Password Validation Error.'
    TECHNICAL_ERROR = 'OOPS!! Something went wrong. Please try again after sometime.'
    MISSING_EMAIL_PASSWORD = 'Email and Password are mandatory!'
    COMPANY_USER_NOT_FOUND = 'Company does not exist.'
    SESSION_ACTIVE = 'Already Loggedin or previous sessions are not closed properly!!'
    EMAIL_PASSWORD_COMBINATION_MISMATCH = 'Invalid Email and Password combination! Please input valid credentials!!'
    SMS_ALERT = '[SMS_ALERT_ERROR]'
    EMAIL_ALERT = '[EMAIL_ALERT_ERROR]'
    INCORRECT_USERNAME_CONTACT_NUMBER = 'Incorrect Contact Number'
    LOGIN_OTP_SENT_SUCCESS = 'OTP sent successfully'
    LOGIN_OTP_ALREADY_SENT = 'OTP already sent, please check or wait for some time to generate new OTP.'
    USER_BLOCKED = 'User Blocked'
    INCORRECT_OTP = 'Incorrect OTP'
    CURRENT_PASSWORD_AND_NEW_PASSWORD_SAME = 'Current password & New password are same.'
    NEW_PASSWORD_AND_CONFIRM_PASSWORD_MISMACTH = 'New password & Confirm password are not same.'
    OLD_PASSWORD_DOESNT_MISMACTH = 'Old password doesnt match.'
    PASSWORD_USED_BEFORE = 'This password was used before you, please use a different password.'
    PASSWORD_UPDATE_FAILED = 'Password update Failed.'
    PASSWORD_GEN_EMAIL_ALREADY_SENT = 'Password Regeneration email already sent, Please try after sometime.'
    PASSWORD_EXPIRED = 'Password Expired, Please Reset the Password.'
    OTP_EXPIRED = 'OTP Expired, please try with another OTP.'
    OTP_USED = 'OTP Already used for verification.'
    OTP_LIMIT_EXCEEDED = 'OTP limit exceeded, try after sometime.'
    USER_BLOCKED_DUE_EXCEEDED_ATTEMPTS = 'User Blocked due to exceeded login attempts with incorrect password/otp'
    USERNAME_OR_PASSWORD_EMPTY = 'Username or Password Empty'
    INVALID_CAPTCHA = 'Invalid Captcha'
    CAPTCHA_CONFIG_NOT_DONE = 'Captcha Config not Done.'
    MISSING_CONTACT_OR_EMAIL = 'Missing Contact or Email'


class RedisChannel:
    REVOKED_TOKENS = 'revoked_tokens'


class DBColls:
    COMPANY_TYPE = 'company_type'
    USER_TYPE = 'user_type'
    USER_ROLES = 'user_roles'
    PERMISSIONS = 'permissions'
    USER_PERMISSIONS = 'user_permissions'
    MODULES = 'modules'
    UI_CONFIG = 'ui_config'
    COMPANIES = 'companies'
    USERS = 'users'
    USER_SESSIONS = 'user_sessions'
    LOGIN_LOGS = 'login_logs'
    USER_ACTIVITY_LOGS = 'user_activity_logs'
    ACCESS_TOKEN = 'access_token'
    OTP_DETAILS = 'otp_details'
    BLOCKED_USERS = 'blocked_users'
    SMS_VENDORS = 'sms_vendors'
    SMS_SENT_LOGS = 'sms_sent_logs'
    API_REQUEST_LOGS = 'api_request_logs'
    STATES_LIST = 'states_list'
    APP_URLS = 'app_urls'
    COMPANY_MAPPING = 'company_mapping'
    FEATURE_GROUPS = 'feature_groups'
    FEATURES = 'features'
    PLANS = 'plans'
    SUBSCRIPTIONS = 'subscriptions'


class AccountStatus(Enum):
    IN_REVIEW = 1
    ACTIVE = 2
    REJECTED = 3


class CompanyType(Enum):
    SEEKER = 1
    PROVIDER = 2


class UserType(Enum):
    SEEKER = 1
    PROVIDER = 2
    DRIVER = 3
    CONSIGNEE = 4
    SCLEN = 5


class UserRole(Enum):
    SUPER_ADMIN = 1
    ADMIN_SEEKER = 10
    SEEKER = 20
    PROVIDER = 30


class SAASProduct(Enum):
    NETWORK = 1
    PROCUREMENT = 2
    OPTIMIZATION = 3
    EXECUTION = 4
    VISIBILITY = 5
    RECONCILIATION = 6
    ANALYTICS = 7
    ORCHESTRATION = 8
    GENERAL_PROCUREMENT = 9


class BillingCycle(Enum):
    MONTHLY = 'monthly'
    ANNUALLY = 'annually'


class SubscriptionStatus(Enum):
    PENDING = 1
    ACTIVE = 2
    PAUSED = 3
    EXPIRED = 4
    CANCELLED = 5
    HALTED = 6
    COMPLETED = 7
    TRIAL = 8
    IN_REVIEW = 9


class UserActivityType:
    LOGIN = 1
    LOGOUT = 2
    CHANGE_PASSWORD = 3
    FORGOT_PASSWORD = 4
    RESET_PASSWORD = 5
    LOGIN_OTP_SENT = 6


class UserActivityMessages:
    LOGIN_SUCCESSFUL = 'Login success'
    INCORRECT_PASSWORD = 'Login failed due to incorrect password'
    INCORRECT_USERNAME = 'Login failed due to incorrect username'
    LOGOUT_SUCCESSFUL = 'Logout success'
    CHANGE_PASS_SUCCESS = 'Password changed successfully'
    FORGOT_PASS_SUCCESS = 'Forgot password email sent successfully'
    RESET_PASS_SUCCESS = 'Password reset successful'
    LOGIN_OTP_SENT_SUCCESS = 'OTP sent successfully'
    USER_BLOCKED = 'User Blocked'
    INCORRECT_OTP = 'Incorrect OTP'


class EmailHeader:
    SCLEN_RESET_PASSWORD = 'SCLEN Reset Password'
    SCLEN_VERIFY_EMAIL = 'SCLEN Registration: Email Verification'


class SMSVendor:
    BOSCHINDIA = 2


class OTPConstant:
    OTP_LIMIT = 3           # 3 TIMES
    EXPIRE_TIME = 300000    # 5 MINUTES
    EXPIRE_TIME_IN_MINS = 5  # 5 MINUTES
    OTP_WINDOW = 5          # 5 MINUTE


class LoginConstant:
    NO_OF_ATTEMPT = 5
    RESET_TOKEN_EXPIRY = 900000  # 15min


class LoginType(Enum):
    PASSWORD_LOGIN = 1
    OTP_LOGIN = 2


class TokenType(Enum):
    PASSWORD_RESET = 1
    PASSWORD_EXPIRED = 2
