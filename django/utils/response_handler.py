import math
import logging
from django.conf import settings
from rest_framework.response import Response
from rest_framework.views import exception_handler
from .common import get_traceback
from .application import SessionUtils

logger = logging.getLogger('application')


class ErrorResponse(object):
    API_ERROR = 'api_error'
    AUTHENTICATION_ERROR = 'authentication_error'
    INVALID_REQUEST_ERROR = 'invalid_request_error'
    RATE_LIMIT_ERROR = 'rate_limit_error'
    VALIDATION_ERROR = 'validation_error'
    ACCESS_DENIED = 'access_denied'

    @staticmethod
    def get_response_obj(error_type, message, status=None):
        resp = {
            'type': error_type,
            'message': message
        }
        if status:
            resp['status'] = int(status)
        return resp


def format_response(status, data, message, redirect_to=None, set_cookies=False, delete_cookies=False):
    response = {
        "status": status,
        "data": data,
        "message": message,
        "redirect_to": redirect_to
    }
    response = Response(data=response, status=status)

    if set_cookies:
        SessionUtils.set_session_cookie(response, data['token'])
    elif delete_cookies:
        SessionUtils.delete_session_cookie(response)

    return response


def format_error_response(status, error_message='', error=None, pydantic_error=None, redirect_to=None):
    errors_list = []
    if error:
        errors_list = [str(error)]
        if not isinstance(error, str):
            logger.error(f"{get_traceback(error)}")

    if pydantic_error:
        errors_list = pydantic_error.errors(include_context=False, include_url=False)
        if not error_message:
            msg = errors_list[0]['msg']
            try:
                error_message = f"{errors_list[0]['loc'][0]}: {msg}"
            except IndexError:
                error_message = f"{msg}"

    if not settings.DEBUG_LOGS:
        errors_list = []

    response = {
        "status": status,
        "data": {
            "errors": errors_list,
        },
        "redirect_to": redirect_to,
        "message": error_message
    }
    return Response(data=response, status=status)


def custom_exception_handler(exc, context):
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = exception_handler(exc, context)

    if not response:
        return response

    # Now add the HTTP status code to the response.
    if response.status_code in [401, 403]:
        response.status_code = int(response.data.get('status') or response.status_code)
        response.data.update({
            'status': response.status_code,
            'data': response.data.get('data', {}),
            'message': response.data.get('message')
        })
    elif response.status_code == 429:
        response.data.update({
            'status': response.status_code,
            'data': response.data.get('data', {}),
            'message': f'Request limit exceeded. Please retry after {math.ceil(exc.wait / 60)}min(s)'
        })
    return response
