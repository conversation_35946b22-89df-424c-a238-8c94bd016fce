from pymongo import (
    MongoClient,
    InsertOne,
    UpdateOne,
    UpdateMany,
    # ReplaceOne,
    DeleteOne,
    DeleteMany,
    ReturnDocument,
)
from django.conf import settings


def test_mongo_connection(mongo_uri):
    from pymongo import MongoClient
    from pymongo.server_api import ServerApi

    # Create a new client and connect to the server
    client = MongoClient(mongo_uri, server_api=ServerApi('1'))

    # Send a ping to confirm a successful connection
    try:
        client.admin.command('ping')
        print("Pinged your deployment. You successfully connected to MongoDB!")
    except Exception as e:
        print(e)


class MongoConnection:

    db = None
    previous_db_url = None
    client = None

    @staticmethod
    def get_db(db_url=settings.SCLEN_DB_URL, db_name=settings.SCLEN_DB_NAME, use_ssl=settings.SCLEN_SSL, w='majority', read_preference='primary', get_new=False):
        """
            - w = majority specifies that the write operation must be acknowledged by the majority of the replica set members to be considered successful.
            - read_preference = primary specifies how MongoDB should distribute read operations among the members of a replica set, indicates that read operations should be directed to the primary member of the replica set.
        """
        db = MongoConnection.db
        previous_db_url = MongoConnection.previous_db_url
        client = MongoConnection.client

        if (not get_new) and (db is not None) and (previous_db_url == db_url):
            return client

        db_url = db_url.split('?')[0]
        cert = None
        if use_ssl:
            import certifi
            cert = certifi.where()

        connection_params = {
            'ssl': use_ssl,
            'tls': use_ssl,
            'tlsCAFile': cert,
            'readPreference': read_preference,
            'w': w,
            # 'maxPoolSize': 150  # default = 100
        }
        client = MongoClient(db_url, **connection_params)

        db_instance = client[db_name]

        MongoConnection.db = db_instance
        MongoConnection.previous_db_url = db_url
        MongoConnection.client = client
        return client


class MongoUtility(object):

    def __init__(self, db_url=settings.SCLEN_DB_URL, db_name=settings.SCLEN_DB_NAME, use_ssl=settings.SCLEN_SSL, w='majority', read_preference='primary', get_new=False):
        self.client = MongoConnection.get_db(db_url, db_name, use_ssl, w, read_preference, get_new=get_new)
        self.db = self.client[db_name]
        self.bulk_ops = []

    def get_collection(self, collection_name):
        return self.db[collection_name]

    @staticmethod
    def _get_result(response):
        return {
            'acknowledged': response.acknowledged,
            'matched_count': response.matched_count,
            'modified_count': response.modified_count,
            'upserted_id': response.upserted_id,
        }

    def find(self, collection_name, filter_query, data_filter={'_id': 0}, sort=[], find_one=False):
        coll = self.db[collection_name]
        if find_one:
            return coll.find_one(filter_query, data_filter, sort=sort) or {}

        queryset = coll.find(filter_query, data_filter, sort=sort)

        def count(query=filter_query):
            return coll.count_documents(query)

        setattr(queryset, 'count', count)
        return queryset

    def aggregate(self, collection_name, match_query={}, group_query={}, project_query={}, sort_query={}, allow_disk_use=False):
        coll = self.db[collection_name]
        aggregate_items = []
        if match_query:
            aggregate_items.append({'$match': match_query})
        if group_query:
            aggregate_items.append({'$group': group_query})
        if sort_query:
            aggregate_items.append({'$sort': sort_query})
        if project_query:
            aggregate_items.append({'$project': project_query})
        return coll.aggregate(aggregate_items, allowDiskUse=allow_disk_use)

    def distinct(self, collection_name, field_name, filter_query={}):
        coll = self.db[collection_name]
        return list(coll.distinct(field_name, filter_query))

    def update(
        self, collection_name, filter_query,
        set_query={}, push_query={}, pull_query={}, inc_query={}, unset_query={},
        update_many=False, upsert=False,
        find_one_and_update=False, data_filter={'_id': 0}, sort=None,
        return_document=ReturnDocument.AFTER
    ):
        coll = self.db[collection_name]
        update_query = {}
        if set_query:
            update_query['$set'] = set_query
        if unset_query:
            update_query['$unset'] = unset_query
        if push_query:
            update_query['$push'] = push_query
        if pull_query:
            update_query['$pull'] = pull_query
        if inc_query:
            update_query['$inc'] = inc_query

        if update_many:
            response = coll.update_many(filter_query, update_query, upsert=upsert)
            return self._get_result(response)
        elif find_one_and_update:
            return coll.find_one_and_update(
                filter_query, update_query,
                projection=data_filter,
                sort=sort, upsert=upsert,
                return_document=return_document
            )
        response = coll.update_one(filter_query, update_query, upsert=upsert)
        return self._get_result(response)

    def insert(self, collection_name, new_documents=[], insert_many=False):
        coll = self.db[collection_name]
        if insert_many:
            return coll.insert_many(new_documents)
        return coll.insert_one(new_documents[0])

    def delete(self, collection_name, filter_query, delete_many=False):
        coll = self.db[collection_name]
        if filter_query:
            if delete_many:
                return coll.delete_many(filter_query)
            return coll.delete_one(filter_query)

    def list_indexes(self, collection_name):
        coll = self.db[collection_name]
        return list(coll.list_indexes())

    def create_index(self, collection_name, index_items=[], **kwargs):
        coll = self.db[collection_name]
        if index_items:
            return coll.create_index(index_items, **kwargs)
        return None

    def list_collections(self):
        return self.db.list_collection_names()

    def add_write_operation(
        self,
        filter_query={},
        set_query={}, push_query={}, pull_query={}, inc_query={}, unset_query={},
        insert_one={},
        update_one=False,
        update_many=False,
        upsert=False,
        delete_one=False,
        delete_many=False,
    ):
        op = None
        update_query = {}
        if update_one or update_many:
            if set_query:
                update_query['$set'] = set_query
            # if unset_query:
            #     update_query['$unset'] = unset_query
            # if push_query:
            #     update_query['$push'] = push_query
            # if pull_query:
            #     update_query['$pull'] = pull_query
            # if inc_query:
            #     update_query['$inc'] = inc_query

        if update_one:
            op = UpdateOne(filter=filter_query, update=update_query, upsert=upsert)
        elif update_many:
            op = UpdateMany(filter=filter_query, update=update_query, upsert=upsert)
        elif insert_one:
            op = InsertOne(document=insert_one)
        elif delete_one:
            op = DeleteOne(filter=filter_query)
        elif delete_many:
            op = DeleteMany(filter=filter_query)

        if op:
            self.bulk_ops.append(op)

    def bulk_write(self, collection_name, ordered=True):
        result = {}
        if self.bulk_ops:
            coll = self.db[collection_name]
            result = coll.bulk_write(self.bulk_ops, ordered=ordered).bulk_api_result
            self.bulk_ops = []
        return result
