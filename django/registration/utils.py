import logging
from utils import send_email, J<PERSON>TToken, Memoization
from utils.constants import (
    EmailHeader,
    AppDomainID,
    AppPathID
)

logger = logging.getLogger('application')


def send_verification_email(user_data: dict = None, company_data: dict = None) -> None:
    email_template = 'email_verification.html'

    verify_user = bool(user_data)
    if company_data:
        company_email = company_data['email']
        purpose = JWTToken.Purpose.VERIFY_COMPANY

        # send single email if admin and company emails are same
        if user_data and (company_email == user_data['email']):
            verify_user = False
            purpose = JWTToken.Purpose.VERIFY_COMPANY_AND_USER

        payload = {'email': company_email}
        jwt_token = JWTToken.get_token(payload, company_data['id'], purpose, JWTToken.Expiry.EMAIL_VERIFICATION)
        verification_link = f"{Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_VERIFY_EMAIL)['url']}?token={jwt_token}"

        message = {
            'entity_name': company_data['name'],
            'verification_link': verification_link,
        }
        send_email([company_email], ccs=[], subject=EmailHeader.SCLEN_VERIFY_EMAIL, message=message, template=email_template)

    if verify_user:
        user_email = user_data['email']
        payload = {'email': user_email}
        jwt_token = JWTToken.get_token(payload, user_data['id'], JWTToken.Purpose.VERIFY_USER, JWTToken.Expiry.EMAIL_VERIFICATION)
        verification_link = f"{Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_VERIFY_EMAIL)['url']}?token={jwt_token}"

        message = {
            'entity_name': user_data['user_name'],
            'verification_link': verification_link,
            'token': jwt_token,
        }
        send_email([user_email], ccs=[], subject=EmailHeader.SCLEN_VERIFY_EMAIL, message=message, template=email_template)
