import re
from typing_extensions import Self
from pydantic import (
    BaseModel, EmailStr, Field,
    field_validator, model_validator
)
from utils import Memoization, is_valid_domain

GSTIN_REGEX = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$'
PAN_REGEX = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'


class AdminUserDetails(BaseModel):
    user_name: str = Field(..., min_length=3)
    email: EmailStr
    phone: int
    password: str

    @field_validator('email', mode='after')
    def validate_email(cls, value: str) -> str:
        if not is_valid_domain(value):
            raise ValueError("Invalid email domain.")
        return value.lower()

    @field_validator('phone', mode='after')
    def validate_phone(cls, value: int) -> int:
        if len(str(value)) != 10:
            raise ValueError("Invalid Phone number. It must be a 10-digit number.")
        return value


class CompanyRegisterationPayloadValidator(BaseModel):
    plan_id: str | None = None
    name: str = Field(..., min_length=3)
    gstin: str = Field(..., description="15-character GSTIN")
    pan: str = Field(..., description="10-character PAN")
    address: str = Field(..., min_length=10)
    city: str = Field(..., min_length=3)
    state: str
    state_code: str
    pincode: int = Field(..., description="6-digit Indian PIN code")
    email: EmailStr
    phone: int = Field(..., description="10-digit Indian Phone number")
    admin_user: AdminUserDetails

    @field_validator('gstin', mode='after')
    def validate_gstin(cls, value: str) -> str:
        if not re.match(GSTIN_REGEX, value):
            raise ValueError("Invalid GSTIN. It must be of 15-character format.")
        return value

    @field_validator('pan', mode='after')
    def validate_pan(cls, value: str) -> str:
        if not re.match(PAN_REGEX, value):
            raise ValueError("Invalid PAN. It must be of 15-character format.")
        return value

    @field_validator('pincode', mode='after')
    def validate_pincode(cls, value: int) -> int:
        if len(str(value)) != 6:
            raise ValueError("Invalid PIN code. It must be a 6-digit number.")
        return value

    @field_validator('email', mode='after')
    def validate_email(cls, value: str) -> str:
        if not is_valid_domain(value):
            raise ValueError("Invalid email domain.")
        return value.lower()

    @field_validator('phone', mode='after')
    def validate_phone(cls, value: int) -> int:
        if len(str(value)) != 10:
            raise ValueError("Invalid Phone number. It must be a 10-digit number.")
        return value

    @model_validator(mode='after')
    def validate_state_fields(self) -> Self:
        states_list = Memoization.get_states_list()
        for item in states_list:
            if (self.state_code == item['id']) and (self.state == item['name']):
                return self
        raise ValueError("Invalid State details.")
