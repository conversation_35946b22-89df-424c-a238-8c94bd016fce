import logging
from pydantic import ValidationError
from django.conf import settings
from throttle import ResendVerificationEmailThrottle
from rest_framework.views import APIView
from rest_framework import status
from utils import (
    format_error_response,
    format_response,
    validate_password,
    encrypt_password,
    SessionUtils,
    J<PERSON><PERSON>oken,
    DateUtil,
    Memoization
)
from utils.constants import (
    CompanyType,
    UserType,
    UserRole,
    DBColls,
    ErrorMessages,
    AppDomainID,
    AppPathID,
    BillingCycle
)
from utils.mongo import MongoUtility
from schema import CompanySchema, UserSchema
from authn import AuthenticateSeekerAdmin
from .request_validators import CompanyRegisterationPayloadValidator
from .utils import send_verification_email

logger = logging.getLogger('application')


class CompanyRegistrationAPI(APIView):

    def post(self, request):
        raw_payload = request.data

        try:
            data = CompanyRegisterationPayloadValidator(**raw_payload)
            payload = data.model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        self.db = MongoUtility()

        admin_user_data = payload.pop('admin_user')
        company_data = payload

        # check if subscription plan exists
        if payload['plan_id']:
            df = {'_id': 0, 'id': 1, 'product_id': 1, 'billing_cycle': 1}
            plan = self.db.find(DBColls.PLANS, {'id': payload['plan_id'], 'is_active': True}, df, find_one=True)
            if not plan:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Subscription Plan does not exist')
        else:
            plan = {}

        # validate admin password
        admin_user_data['pwd_hash'] = encrypt_password(admin_user_data['password'])
        errors = validate_password(admin_user_data['password'], admin_user_data['pwd_hash'], admin_user_data)
        if errors:
            return format_response(status.HTTP_400_BAD_REQUEST, {'validation_errors': errors}, errors[0])

        # process company and user data
        try:
            company_doc = self.prepare_company_data(company_data, plan)
            admin_user_doc = self.prepare_admin_user_data(admin_user_data, company_doc)
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        # save company and user to db
        self.db.insert(DBColls.COMPANIES, [company_doc])
        self.db.insert(DBColls.USERS, [admin_user_doc])
        company_doc.pop('_id', None)
        admin_user_doc.pop('_id', None)

        # generate user session
        session_cls = SessionUtils(admin_user_doc['email'])
        session = session_cls.generate_session(admin_user_doc)
        response_data = {
            'token': session['token'],
            'session_id': session['session_id'],
            'company_id': session['company_id'],
            'company_name': session['company_name'],
            'company_type': session['company_type'],
            'user_id': session['user_id'],
            'user_name': session['user_name'],
            'user_type': session['user_type'],
        }
        redirect_to = Memoization.get_app_url_data(AppDomainID.ADMIN, AppPathID.ADMIN_PROFILE)
        send_verification_email(admin_user_doc, company_doc)
        return format_response(status.HTTP_200_OK, response_data, 'Success', redirect_to)

    def prepare_company_data(self, company_data, plan):
        self.check_if_company_exists(company_data)

        company_data['company_type'] = CompanyType.SEEKER.value
        if plan:
            company_data['initial_plan'] = {
                'plan_id': plan['id'],
                'is_annual': plan['billing_cycle'] == BillingCycle.ANNUALLY.value,
                'product_id': plan['product_id'],
            }

        company_doc = CompanySchema(**company_data).model_dump()
        return company_doc

    def prepare_admin_user_data(self, user_data, company_data):
        self.check_if_user_exists(user_data)

        user_data.update({
            'company_id': company_data['id'],
            'company_name': company_data['name'],
            'company_type': CompanyType.SEEKER.value,
            'user_type': UserType.SEEKER.value,
            'user_role': UserRole.ADMIN_SEEKER.value,
            'pwd_history': [user_data['pwd_hash']],
        })

        user_doc = UserSchema(**user_data).model_dump()
        return user_doc

    def check_if_company_exists(self, company_data):
        validation_fields = ['email', 'phone', 'gstin', 'pan']

        filter_query = {
            '$or': [{field: company_data[field]} for field in validation_fields]
        }
        doc = self.db.find(DBColls.COMPANIES, filter_query, find_one=True)
        if not doc:
            return False

        for field in validation_fields:
            if (field in doc) and (doc[field] == company_data[field]):
                raise ValueError(f'Company with {field}: `{doc[field]}` already exists.')

    def check_if_user_exists(self, user_data):
        validation_fields = ['email', 'phone']

        filter_query = {
            '$or': [{field: user_data[field]} for field in validation_fields]
        }
        doc = self.db.find(DBColls.USERS, filter_query, find_one=True)
        if not doc:
            return False

        for field in validation_fields:
            if (field in doc) and (doc[field] == user_data[field]):
                raise ValueError(f'User with {field}: `{doc[field]}` already exists.')


class VerifyEmailAPI(APIView):

    def post(self, request):
        headers = request.headers
        token = headers.get('token', '')
        redirect_to = Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_VERIFY_EMAIL_FAILED)

        try:
            decoded_payload = JWTToken.decode_token(token)
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e), redirect_to=redirect_to)

        db = MongoUtility()
        now = DateUtil.get_current_timestamp()

        entity_id = decoded_payload['sub']
        email = decoded_payload['email']

        filter_query = {
            'id': entity_id,
            'email': email,
            'email_verified_on': None
        }
        update_query = {
            'email_verified_on': now,
            'updated_on': now
        }

        if decoded_payload['purpose'] == JWTToken.Purpose.VERIFY_USER:
            db.update(DBColls.USERS, filter_query, update_query)
        elif decoded_payload['purpose'] == JWTToken.Purpose.VERIFY_COMPANY:
            db.update(DBColls.COMPANIES, filter_query, update_query)
        elif decoded_payload['purpose'] == JWTToken.Purpose.VERIFY_COMPANY_AND_USER:
            filter_query['company_id'] = filter_query.pop('id')

            db.update(DBColls.USERS, filter_query, update_query)
            db.update(DBColls.COMPANIES, filter_query, update_query)
        else:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid token purpose', redirect_to=redirect_to)

        redirect_to = Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_VERIFY_EMAIL_SUCCESS)
        return format_response(status.HTTP_200_OK, {}, 'Success', redirect_to)


class ResendVerificationEmailAPI(APIView):
    throttle_classes = [ResendVerificationEmailThrottle]
    throttle_scope = 'resend_verification_email'
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request):
        user_id = request.data.get('user_id')
        company_id = request.data.get('company_id')
        user, company = {}, {}

        db = MongoUtility()

        if user_id:
            user = db.find(DBColls.USERS, {'id': user_id, 'is_active': True}, find_one=True)

            if user['email_verified_on']:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'User email has already been verified.')

            company = db.find(DBColls.COMPANIES, {'id': user['company_id'], 'email': user['email'], 'is_active': True}, find_one=True)
        elif company_id:
            company = db.find(DBColls.COMPANIES, {'id': company_id, 'is_active': True}, find_one=True)

            if company['email_verified_on']:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Company email has already been verified.')

            user = db.find(DBColls.USERS, {'company_id': company_id, 'email': company['email'], 'is_active': True}, find_one=True)

        send_verification_email(user_data=user, company_data=company)

        return format_response(status.HTTP_200_OK, {}, 'Success')


class ActivateUserAPI(APIView):

    def post(self, request):
        headers = request.headers
        token = headers.get('token', '')
        redirect_to = Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_LOGIN)

        try:
            decoded_payload = JWTToken.decode_token(token, issuer=settings.ADMIN_API_URL, secret=settings.ADMIN_SECRET_KEY)
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e), redirect_to=redirect_to)

        if decoded_payload['purpose'] != JWTToken.Purpose.ACTIVATE_USER:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid token purpose', redirect_to=redirect_to)

        db = MongoUtility()
        now = DateUtil.get_current_timestamp()

        user_id = decoded_payload['sub']
        email = decoded_payload['email']

        filter_query = {'id': user_id, 'email': email}
        user = db.find(DBColls.USERS, filter_query, find_one=True)
        if not user:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'User not found.', redirect_to=redirect_to)

        if user['is_active']:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'User account is already active.', redirect_to=redirect_to)

        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')

        if new_password != confirm_password:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                ErrorMessages.NEW_PASSWORD_AND_CONFIRM_PASSWORD_MISMACTH,
            )

        new_pwd_hash = encrypt_password(new_password)
        errors = validate_password(new_password, new_pwd_hash, user)
        if errors:
            return format_error_response(status.HTTP_400_BAD_REQUEST, errors[0], errors)

        update_query = {
            'is_active': True,
            'pwd_hash': new_pwd_hash,
            'pwd_history': [new_pwd_hash],
            'pwd_changed_on': now,
            'email_verified_on': now,
            'updated_on': now
        }

        db.update(DBColls.USERS, filter_query, update_query)
        return format_response(status.HTTP_200_OK, {}, 'User account activated', redirect_to)
