from pydantic import BaseModel, EmailStr, Field
from utils import get_uuid, DateUtil
from utils.constants import (
    CompanyType, UserType,
    UserRole, AccountStatus,
    SAASProduct
)


class InitialPlanModel(BaseModel):
    plan_id: str
    is_annual: bool = False
    product_id: SAASProduct

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class CompanySchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    razorpay_customer_id: str | None = None
    is_active: bool = True
    has_subscribed: bool = Field(default=False, description='This is identify if the user had subscribed to any plan atleast once')
    initial_plan: InitialPlanModel | None = Field(default=None, description='This is just to retain the plan details selected initially during registration')
    acc_status: str = AccountStatus.IN_REVIEW.name
    acc_status_id: AccountStatus = AccountStatus.IN_REVIEW.value
    is_trial_used: dict = Field(default={}, description='dictionary of product_id(key) and boolean(value) pairs')
    company_type: CompanyType
    name: str
    gstin: str
    pan: str
    address: str
    city: str
    state: str
    state_code: str
    pincode: int
    email: EmailStr
    phone: int
    email_verified_on: int = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class UserSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    is_active: bool = True
    company_id: str
    company_name: str
    company_type: CompanyType
    user_name: str
    email: EmailStr
    phone: int
    user_type: UserType
    user_role: UserRole
    pwd_hash: str
    pwd_changed_on: str = None
    pwd_history: list
    email_verified_on: int = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values
